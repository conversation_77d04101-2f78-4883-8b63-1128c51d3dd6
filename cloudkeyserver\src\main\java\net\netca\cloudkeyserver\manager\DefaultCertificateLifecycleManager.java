package net.netca.cloudkeyserver.manager;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.constant.*;
import net.netca.cloudkey.base.po.*;
import net.netca.cloudkey.base.util.CommonUtil;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.SoftCertMessage;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.req.DecryptSoPinRequest;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.req.RegisterRequest;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.vo.SoPin;
import net.netca.cloudkey.lifecycle.constant.BpmsRequestStatusConstant;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 默认证书生命周期管理器实现
 *
 * 基于BPMS业务平台的证书生命周期管理，继承抽象类实现。
 * 专注于BPMS平台特定的交互逻辑，通用业务逻辑由抽象类处理。
 *
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Slf4j
@Component("defaultCertificateLifecycleManager")
public class DefaultCertificateLifecycleManager extends AbstractCertificateLifecycleManager {

    // ==================== 抽象方法实现 ====================

    @Override
    protected NetcaBpmsResponse doApplyCertificate(RegisterRequest registerRequest,
                                                  String opSignature,
                                                  BusinessUser businessUser,
                                                  ConfigProject configProject,
                                                  String url) throws Exception {

        String requestJson = CommonUtil.toJSONString(registerRequest);
        SoftCertMessage softCertMessage = createSoftCertMessage();

        String targetUrl;
        if (url != null) {
            // 事件证书申请使用传入的URL
            targetUrl = url;
        } else {
            // 普通证书申请使用配置的URL
            targetUrl = configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.BPMS_URL_PREFIX.getCode()) +
                       configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.BPMS_REGISTER_CERT_URL.getCode());
        }

        NetcaBpmsResponse bpmsResponse = certLifeCycleService.certApplyRequestBpms(requestJson, opSignature, targetUrl, softCertMessage);
        String bpmsReqId = bpmsResponse.getReqId();
        log.info("业务平台单号为，bpmsReqId=" + bpmsReqId);

        if (CommonUtil.isStringEmpty(bpmsReqId)) {
            throw new RuntimeException("业务平台返回的业务单号为空");
        }

        return bpmsResponse;
    }

    @Override
    protected NetcaBpmsResponse doQueryCertificateStatus(String systemId, String requestId) throws Exception {
        String url = configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.BPMS_URL_PREFIX.getCode()) +
                    configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.BPMS_REQUEST_SEARCH_URL.getCode());

        Map<String, String> params = new HashMap<>();
        params.put("systemId", systemId);
        params.put("reqId", requestId);

        String requestJson = CommonUtil.toJSONString(params);
        SoftCertMessage softCertMessage = createSoftCertMessage();

        return certLifeCycleService.getBpmsResponse(requestJson, url, softCertMessage, null);
    }

    @Override
    protected NetcaBpmsResponse doDownloadCertificate(String requestId, String systemId) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("systemId", systemId);
        params.put("reqId", requestId);

        String requestJson = CommonUtil.toJSONString(params);
        String bpmsDownLoadCertUrl = configKeyValueCacheUtil.selectConfigValueByKey(
                ConfigKeyValueEnum.BPMS_URL_PREFIX.getCode()) +
                configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.BPMS_DOWNLOAD_CERT_URL.getCode());

        SoftCertMessage softCertMessage = createSoftCertMessage();

        return certLifeCycleService.getBpmsResponse(requestJson, bpmsDownLoadCertUrl, softCertMessage, null);
    }

    @Override
    protected String doDecryptAdministratorPin(String encryptedPin, String systemId) {
        DecryptSoPinRequest decryptSoPinRequest = new DecryptSoPinRequest();
        SoPin soPin = new SoPin();
        soPin.setSoPin(encryptedPin);
        decryptSoPinRequest.setSoPin(soPin);
        decryptSoPinRequest.setSystemId(systemId);

        try {
            String requestJson = CommonUtil.toJSONString(decryptSoPinRequest);
            SoftCertMessage softCertMessage = createSoftCertMessage();
            String url = configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.BPMS_URL_PREFIX.getCode()) +
                        configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.BPMS_DEC_ADMIN_PIN_URL.getCode());

            return certLifeCycleService.dectyprBpmsAdmin(requestJson, url, softCertMessage, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("解密管理PIN失败" + e.getMessage());
        }
    }

    @Override
    protected NetcaBpmsResponse doRevokeCertificate(BusinessCertAttribute businessCertAttribute,
                                                   ConfigProject configProject,
                                                   AuthorityOperator authorityOperator) throws Exception {

        String bpmsRevokeCertUrl = configKeyValueCacheUtil.selectConfigValueByKey(
                ConfigKeyValueEnum.BPMS_URL_PREFIX.getCode()) +
                configKeyValueCacheUtil.selectConfigValueByKey(
                        ConfigKeyValueEnum.BPMS_REVOKE_CERT_URL.getCode());

        // 构建请求参数（这部分逻辑比较复杂，暂时简化处理）
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", configProject.getBpmsProjectId());
        params.put("systemId", configProject.getBpmsSystemId());
        params.put("businessCenterId", configProject.getBpmsBusinessCenterId());
        params.put("templateId", configProject.getBpmsCertTemplateId());

        String requestJson = CommonUtil.toJSONString(params);
        SoftCertMessage softCertMessage = createSoftCertMessage();

        return certLifeCycleService.getBpmsResponse(requestJson, bpmsRevokeCertUrl, softCertMessage, null);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建软证书消息对象
     */
    private SoftCertMessage createSoftCertMessage() {
        SoftCertMessage softCertMessage = new SoftCertMessage();
        softCertMessage.setCertEntry(systemConfig.getCertEntry());
        softCertMessage.setCertPassword(systemConfig.getCertPassword());
        softCertMessage.setKeyStoreFile(systemConfig.getCertKeyStorePath());
        softCertMessage.setKeyStoreType(systemConfig.getCertStoreType());
        softCertMessage.setKeyStorePassWord(systemConfig.getCertStorePWD());
        return softCertMessage;
    }

    // ==================== 业务信息处理方法 ====================
    // saveBusinessInfoBy 方法已移动到 AbstractCertificateLifecycleManager 中作为通用实现
    // DefaultCertificateLifecycleManager 直接使用父类的通用实现，无需重写

    // ==================== updateBusinessInfo 抽象策略方法实现 ====================

    @Override
    protected Map<String, Object> parseResponseData(NetcaBpmsResponse bpmsResponse) throws Exception {
        // 使用 certLifeCycleService 解析 BPMS 响应
        Map<String, Map<String, Object>> bpmsRespResult = certLifeCycleService.getCertMessageFromBpmsResp(bpmsResponse);

        Map<String, Object> responseData = new HashMap<>();
        responseData.put("bpmsRespResult", bpmsRespResult);
        responseData.put("reqId", bpmsResponse.getReqId());
        responseData.put("status", bpmsResponse.getStatus());

        return responseData;
    }

    @Override
    protected BusinessResponseStatus mapResponseStatus(NetcaBpmsResponse bpmsResponse) {
        Long status = bpmsResponse.getStatus();

        if (BpmsRequestStatusConstant.REQ_STATUS_SUCCESS.getCode().equals(status)) {
            return BusinessResponseStatus.SUCCESS;
        } else if (BpmsRequestStatusConstant.REQ_STATUS_ENTER_REQ.getCode().equals(status)) {
            return BusinessResponseStatus.PENDING;
        } else {
            return BusinessResponseStatus.FAILURE;
        }
    }

    @Override
    protected Map<String, Map<String, Object>> extractCertificateInfo(Map<String, Object> responseData) throws Exception {
        @SuppressWarnings("unchecked")
        Map<String, Map<String, Object>> bpmsRespResult =
            (Map<String, Map<String, Object>>) responseData.get("bpmsRespResult");

        return bpmsRespResult;
    }

    @Override
    protected Map<String, Object> handleSuccessResponse(Map<String, Object> responseData,
                                                       int businessCertId, int userId) throws Exception {
        // BPMS平台特定的成功处理逻辑
        // 注意：证书属性处理、业务证书更新、密钥对处理等通用逻辑已在抽象基类的模板方法中处理
        // 这里只需要处理BPMS平台特有的逻辑（如果有的话）

        log.debug("BPMS平台成功响应处理，业务证书ID: {}, 用户ID: {}", businessCertId, userId);

        // BpmsManager 中没有返回额外的处理结果，这里保持一致
        // 只返回空的结果集，实际的业务逻辑由抽象基类的模板方法处理
        return new HashMap<>();
    }

    @Override
    protected Map<String, Object> handlePendingResponse(Map<String, Object> responseData,
                                                       BusinessUser businessUser) throws Exception {
        // BPMS平台特定的待审核处理逻辑
        // 注意：用户状态更新等通用逻辑已在抽象基类的模板方法中处理
        // 这里只需要处理BPMS平台特有的逻辑（如果有的话）

        log.debug("BPMS平台待审核响应处理，用户ID: {}", businessUser.getId());

        // BpmsManager 中没有返回额外的处理结果，这里保持一致
        return new HashMap<>();
    }

    @Override
    protected Map<String, Object> handleFailureResponse(Map<String, Object> responseData,
                                                       NetcaBpmsResponse bpmsResponse) throws Exception {
        // BPMS平台特定的失败处理逻辑
        // 注意：错误信息记录、业务请求状态更新等通用逻辑已在抽象基类的模板方法中处理
        // 这里只需要处理BPMS平台特有的逻辑（如果有的话）

        log.debug("BPMS平台失败响应处理，状态码: {}", bpmsResponse.getStatus());

        // BpmsManager 中没有返回额外的处理结果，这里保持一致
        return new HashMap<>();
    }

    @Override
    protected String formatErrorMessage(NetcaBpmsResponse bpmsResponse, String defaultMessage) {
        // 默认BPMS平台的错误信息格式化
        return defaultMessage;
    }



    // ==================== 密钥对处理辅助方法 ====================
    // saveSignKeyPair 方法已移动到 AbstractCertificateLifecycleManager 中作为通用实现
    // DefaultCertificateLifecycleManager 直接使用父类的通用实现，无需重写

    // doUpdateSignKeyPairAndSaveEncKeyPair 方法已移动到 AbstractCertificateLifecycleManager 中作为通用实现
    // DefaultCertificateLifecycleManager 直接使用父类的通用实现，无需重写
}