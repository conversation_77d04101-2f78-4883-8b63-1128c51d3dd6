package net.netca.cloudkeyserver.manager;

import cn.hutool.core.util.ObjectUtil;

import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.constant.*;
import net.netca.cloudkey.base.javaconfig.SystemConfig;
import net.netca.cloudkey.base.po.*;
import net.netca.cloudkey.base.service.*;
import net.netca.cloudkey.base.sms.SmsHandlerContext;
import net.netca.cloudkey.base.sms.SmsHandlerService;
import net.netca.cloudkey.base.util.*;
import net.netca.cloudkey.pki.device.KeyPairManagerFactory;
import net.netca.cloudkey.pki.util.CryptoUtil;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.*;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.req.RegisterRequest;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.vo.ResponseResult;
import net.netca.cloudkey.lifecycle.service.CertLifeCycleService;
import net.netca.cloudkey.pki.device.ThreadLocalEncryptionCardKeyPairManager;
import net.netca.cloudkey.pki.device.subject.SubjectDN;
import net.netca.cloudkey.pki.util.SystemDeviceCryptoDataUtil;
import net.netca.cloudkeyserver.service.SealWebService;
import net.netca.cloudkeyserver.util.RequestIdGenerator;
import net.netca.netcasvs.jni.KeyPairWrapper;
import net.netca.pki.KeyPair;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 证书生命周期管理抽象类
 *
 * 提供证书生命周期管理的通用业务逻辑实现，包括：
 * - 通用的数据验证和工具方法
 * - 证书请求生成的通用逻辑
 * - 业务数据处理的通用流程
 * - 模板方法模式的标准业务流程
 *
 * 子类需要实现具体的CA平台交互逻辑。
 *
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Slf4j
public abstract class AbstractCertificateLifecycleManager implements ICertificateLifecycleManager {

    // ==================== 依赖注入 ====================

    @Autowired
    protected ThreadLocalEncryptionCardKeyPairManager threadLocalKeyPairManager;

    @Autowired
    protected BusinessUserService businessUserService;

    @Autowired
    protected BusinessLinkManService businessLinkManService;

    @Autowired
    protected BusinessCertService businessCertService;

    @Autowired
    protected BusinessRequestService businessRequestService;

    @Autowired
    protected BusinessCertAttributeService businessCertAttributeService;

    @Autowired
    protected BusinessKeypairInfoService businessKeypairInfoService;

    @Autowired
    protected SystemConfig systemConfig;

    @Autowired
    protected SystemDeviceCryptoDataUtil systemDeviceCryptoDataUtil;

    @Autowired
    protected SmsHandlerContext smsHandlerContext;

    @Autowired
    protected CertLifeCycleService certLifeCycleService;

    @Autowired
    protected BusinessKeypairInfoEncParameterService businessKeypairInfoEncParameterService;

    @Resource
    protected ConfigKeyValueCacheUtil configKeyValueCacheUtil;

    @Autowired
    protected SealWebService sealWebService;

    @Autowired
    protected SealPicService sealPicService;

    // ==================== 抽象方法定义 ====================

    /**
     * 具体的证书申请实现
     * 子类需要实现与具体CA平台的交互逻辑
     *
     * @param registerRequest 证书申请请求
     * @param opSignature 操作签名
     * @param businessUser 业务用户
     * @param configProject 项目配置
     * @param url 申请URL（可选）
     * @return 业务平台响应
     * @throws Exception 申请过程中的异常
     */
    protected abstract NetcaBpmsResponse doApplyCertificate(RegisterRequest registerRequest,
                                                            String opSignature,
                                                            BusinessUser businessUser,
                                                            ConfigProject configProject,
                                                            String url) throws Exception;

    /**
     * 具体的证书状态查询实现
     *
     * @param systemId 系统ID
     * @param requestId 请求ID
     * @return 业务平台响应
     * @throws Exception 查询过程中的异常
     */
    protected abstract NetcaBpmsResponse doQueryCertificateStatus(String systemId, String requestId) throws Exception;

    /**
     * 具体的证书下载实现
     *
     * @param requestId 请求ID
     * @param systemId 系统ID
     * @return 业务平台响应
     * @throws Exception 下载过程中的异常
     */
    protected abstract NetcaBpmsResponse doDownloadCertificate(String requestId, String systemId) throws Exception;

    /**
     * 具体的管理员PIN解密实现
     *
     * @param encryptedPin 加密的PIN
     * @param systemId 系统ID
     * @return 解密后的PIN
     */
    protected abstract String doDecryptAdministratorPin(String encryptedPin, String systemId);

    /**
     * 具体的证书注销实现
     *
     * @param businessCertAttribute 证书属性
     * @param configProject 项目配置
     * @param authorityOperator 授权操作员
     * @return 业务平台响应
     * @throws Exception 注销过程中的异常
     */
    protected abstract NetcaBpmsResponse doRevokeCertificate(BusinessCertAttribute businessCertAttribute,
                                                             ConfigProject configProject,
                                                             AuthorityOperator authorityOperator) throws Exception;

    // ==================== 接口方法实现（模板方法） ====================

    @Override
    public NetcaBpmsResponse queryCertificateApplicationStatus(String systemId, String requestId) throws Exception {
        Preconditions.checkNotNull(systemId, "业务平台第三方系统id不能为空");
        Preconditions.checkNotNull(requestId, "业务平台业务单号不能为空");

        log.info("===================查询业务平台业务进度开始=============================================");
        log.info("systemId为：" + systemId);
        log.info("requestId为：" + requestId);

        return doQueryCertificateStatus(systemId, requestId);
    }

    @Override
    public NetcaBpmsResponse downloadCertificate(String requestId, String systemId) throws Exception {
        Preconditions.checkNotNull(systemId, "业务平台第三方系统id不能为空");
        Preconditions.checkNotNull(requestId, "业务平台业务单号不能为空");

        log.info("===================从业务平台下载证书开始=============================================");
        log.info("systemId为：" + systemId);
        log.info("requestId为：" + requestId);

        return doDownloadCertificate(requestId, systemId);
    }

    @Override
    public String decryptAdminPin(String encryptedPin, String systemId) {
        return doDecryptAdministratorPin(encryptedPin, systemId);
    }

    @Override
    public Boolean revokeCertificate(BusinessCertAttribute businessCertAttribute,
                                     ConfigProject configProject,
                                     AuthorityOperator authorityOperator) throws Exception {

        log.info("===============注销证书开始==========");
        boolean isSuccess = false;

        Preconditions.checkNotNull(businessCertAttribute, "businessCertAttribute is null");
        Preconditions.checkNotNull(configProject, "configProject is null");
        Preconditions.checkNotNull(authorityOperator, "authorityOperator is null");

        log.info(String.format("证书序列号为：%s", businessCertAttribute.getCertSn()));
        log.info(String.format("证书用法为：%d", businessCertAttribute.getCertUsage()));

        // 1. 创建业务请求记录
        BusinessRequest businessRequest = createRevokeBusinessRequest(businessCertAttribute, configProject, authorityOperator);

        NetcaBpmsResponse netcaBpmsResponse = null;
        try {
            // 3. 调用具体的CA平台实现进行证书注销
            netcaBpmsResponse = doRevokeCertificate(businessCertAttribute, configProject, authorityOperator);
            log.info("===============注销证书结束：返回结果为==========");
            log.info(CommonUtil.toJSONString(netcaBpmsResponse));

            // 4. 处理注销响应结果
            isSuccess = processRevokeResponse(netcaBpmsResponse, businessRequest);

        } catch (Exception e) {
            handleRevokeError(e, netcaBpmsResponse, businessRequest);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            // 5. 保存业务请求记录
            saveRevokeBusinessRequest(businessRequest);
        }

        return isSuccess;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NetcaBpmsResponse applyCertificate(RegisterRequest registerRequest,
                                              String opSignature,
                                              BusinessUser businessUser,
                                              ConfigProject configProject,
                                              KeyPairWrapper signKeyPair,
                                              String userPin,
                                              String adminPin,
                                              boolean sendSms) {

        log.info("开始申请证书，用户ID: " + businessUser.getId() + ", 项目ID: " + configProject.getId());

        String reqId = null;
        NetcaBpmsResponse bpmsResponse;
        String bpmsReqId;
        boolean isNotifyUser;
        int businessCertId;

        try {
            // 1. 调用具体的CA平台实现进行证书申请
            bpmsResponse = doApplyCertificate(registerRequest, opSignature, businessUser, configProject, null);
            bpmsReqId = bpmsResponse.getReqId();
            log.info("业务平台单号为，bpmsReqId=" + bpmsReqId);
            if (CommonUtil.isStringEmpty(bpmsReqId)) {
                throw new RuntimeException("业务平台返回的业务单号为空");
            }

            // 2. 保存业务信息
            Map<String, Object> saveBusinessInfoResult = saveBusinessInfoBy(
                    businessUser.getId(),
                    configProject.getId(),
                    configProject.getCertUserType(),
                    registerRequest.getCert().getP10(),
                    registerRequest.getLinkman(),
                    signKeyPair,
                    userPin,
                    adminPin,
                    bpmsReqId);

            // 3. 更新业务信息
            businessCertId = (int) saveBusinessInfoResult.get("certId");
            reqId = (String) saveBusinessInfoResult.get("requestId");
            int userId = (int) saveBusinessInfoResult.get("userId");

            isNotifyUser = updateBusinessInfo(bpmsResponse, businessCertId, reqId, userPin, adminPin, userId);

            // 4. 处理签章绑定
            handleSealBinding(userId, businessCertId);

        } catch (Exception e) {
            handleCertificateApplicationError(reqId, e);
            throw new RuntimeException("申请证书失败：" + e.getMessage(), e);
        }

        // 5. 发送通知
        if (sendSms) {
            handleSmsNotifications(businessUser, reqId, userPin, businessCertId, isNotifyUser);
        }

        return bpmsResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NetcaBpmsResponse applyEventCertificate(RegisterRequest registerRequest,
                                                   String opSignature,
                                                   BusinessUser businessUser,
                                                   ConfigProject configProject,
                                                   KeyPairWrapper signKeyPair,
                                                   String userPin,
                                                   String adminPin,
                                                   String url) {

        log.info("开始申请事件证书，用户ID: " + businessUser.getId() + ", 项目ID: " + configProject.getId());

        String reqId = null;
        NetcaBpmsResponse bpmsResponse;
        String bpmsReqId;
        int businessCertId;

        try {
            // 1. 调用具体的CA平台实现进行事件证书申请
            bpmsResponse = doApplyCertificate(registerRequest, opSignature, businessUser, configProject, url);
            bpmsReqId = bpmsResponse.getReqId();
            log.info("业务平台单号为，bpmsReqId=" + bpmsReqId);

            if (CommonUtil.isStringEmpty(bpmsReqId)) {
                throw new RuntimeException("业务平台返回的业务单号为空");
            }

            // 2. 保存业务信息
            Map<String, Object> saveBusinessInfoResult = saveBusinessInfoBy(
                    businessUser.getId(),
                    configProject.getId(),
                    configProject.getCertUserType(),
                    registerRequest.getCert().getP10(),
                    registerRequest.getLinkman(),
                    signKeyPair,
                    userPin,
                    adminPin,
                    bpmsReqId);

            // 3. 更新业务信息
            businessCertId = (int) saveBusinessInfoResult.get("certId");
            reqId = (String) saveBusinessInfoResult.get("requestId");
            int userId = (int) saveBusinessInfoResult.get("userId");

            updateBusinessInfo(bpmsResponse, businessCertId, reqId, userPin, adminPin, userId);

            // 4. 处理签章绑定（事件证书也需要处理签章）
            handleSealBinding(userId, businessCertId);

        } catch (Exception e) {
            handleCertificateApplicationError(reqId, e);
            throw new RuntimeException("申请事件证书失败：" + e.getMessage(), e);
        }

        return bpmsResponse;
    }

    // ==================== 通用工具方法 ====================

    /**
     * 检查证书类型与身份类型的匹配性
     *
     * @param certClass 证书类型
     * @param identityType 身份类型
     * @throws RuntimeException 当类型不匹配时抛出异常
     */
    protected final void checkCertClass(Integer certClass, Long identityType) {
        if (certClass.intValue() == CertUserTypeEnum.PERSON.getCode().intValue()) {
            if (isOrgType(identityType)) {
                throw new RuntimeException(String.format("当前申请证书的证书类型是%s，传入证件类型是%s，请使用正确的证件类型",
                        CertUserTypeEnum.PERSON.getDescription(), CertUserTypeEnum.ORG.getDescription()));
            }
        } else if (certClass.intValue() == CertUserTypeEnum.ORG.getCode().intValue()) {
            if (!isOrgType(identityType)) {
                throw new RuntimeException(String.format("当前申请证书的证书类型是%s，传入证件类型是%s，请使用正确的证件类型",
                        CertUserTypeEnum.ORG.getDescription(), CertUserTypeEnum.PERSON.getDescription()));
            }
        } else {
            throw new RuntimeException(String.format("当前申请证书的证书类型是%s，证书类型非法", certClass + ""));
        }
    }

    /**
     * 判断身份类型是否为组织类型
     *
     * @param identityType 身份类型
     * @return true表示组织类型，false表示个人类型
     * @throws RuntimeException 当身份类型未知时抛出异常
     */
    protected final boolean isOrgType(Long identityType) {
        String orgDesc = OrgIdentityTypeEnum.getDescription(identityType.intValue());
        String personDesc = PersonIdentityTypeEnum.getDescription(identityType.intValue());
        if (orgDesc != null) {
            return true;
        } else if (personDesc != null) {
            return false;
        } else {
            throw new RuntimeException("未知的证件类型");
        }
    }

    /**
     * 创建证书属性对象
     *
     * @param certMess 证书信息Map
     * @param certUsage 证书用途
     * @param businessCertId 业务证书ID
     * @param userId 用户ID
     * @return 证书属性对象
     */
    protected final BusinessCertAttribute createBusinessCertAttribute(Map<String, Object> certMess,
                                                                      Integer certUsage,
                                                                      Integer businessCertId,
                                                                      Integer userId) {
        //保存证书属性信息
        BusinessCertAttribute businessCertAttribute = new BusinessCertAttribute();
        String certSn = (String) certMess.get("certSn");
        byte[] certContent = (byte[]) certMess.get("certContent");
        Integer computeThumbprintAlgo = (Integer) certMess.get("computeThumbprintAlgo");
        String certThumbprint = (String) certMess.get("certThumbprint");
        Date certValidityStart = (Date) certMess.get("certValidityStart");
        Date certValidityEnd = (Date) certMess.get("certValidityEnd");
        String o = (String) certMess.get("o");
        String ou = (String) certMess.get("ou");
        String cn = (String) certMess.get("cn");

        businessCertAttribute.setCertContent(certContent);
        businessCertAttribute.setCertId(businessCertId);
        businessCertAttribute.setCertSn(certSn);
        businessCertAttribute.setCertThumbprint(certThumbprint);
        businessCertAttribute.setCertThumbprintAlgo(computeThumbprintAlgo);
        businessCertAttribute.setPreCertSn(null);
        businessCertAttribute.setO(o);
        businessCertAttribute.setOu(ou);
        businessCertAttribute.setCn(cn);
        businessCertAttribute.setValidityStart(certValidityStart);
        businessCertAttribute.setValidityEnd(certValidityEnd);

        businessCertAttribute.setUserId(userId);
        businessCertAttribute.setCertUsage(certUsage);

        //businessCertAttribute.setStatus(CertStatusEnum.CERT_STATUS_NORMAL.getCode());
        return businessCertAttribute;
    }

    // ==================== 证书请求生成方法 ====================

    /**
     * 准备证书申请请求的基础信息
     *
     * @param businessUser 业务用户
     * @param configProject 项目配置
     * @param configLinkman 联系人配置
     * @return 基础的证书申请请求对象
     */
    protected final RegisterRequest prepareRequest(BusinessUser businessUser,
                                                   ConfigProject configProject,
                                                   ConfigLinkman configLinkman) {
        int certAlgoType = configProject.getCertAlgoType();

        //生成证书请求
        SubjectDN subjectDN = new SubjectDN();
        subjectDN.setCn(businessUser.getName());
        subjectDN.setEmail(businessUser.getEmail());
        subjectDN.setC(businessUser.getCountryName());
        subjectDN.setSt(businessUser.getProvince());
        subjectDN.setL(businessUser.getCity());
        if (isOrgType(Long.valueOf(businessUser.getIdentityType()))) {
            subjectDN.setO(businessUser.getName());
            subjectDN.setOu(businessUser.getName());
        }

        String p10 = null;
        if (CertAlgoTypeEnum.SM2.getCode() == certAlgoType) {//sm2证书
            p10 = threadLocalKeyPairManager.genP10BySM2KeyPair(subjectDN);
        } else {//rsa证书
            p10 = threadLocalKeyPairManager.genP10ByRSAKeyPair(subjectDN);
        }

        RegisterRequest registerRequest = new RegisterRequest();
        //业务平台信息
        registerRequest.setProjectId(configProject.getBpmsProjectId());
        registerRequest.setSystemId(configProject.getBpmsSystemId());
        registerRequest.setBusinessCenterId(Long.valueOf(configProject.getBpmsBusinessCenterId()));
        registerRequest.setTemplateId(configProject.getBpmsCertTemplateId());

        //证书信息
        Cert cert = new Cert();
        cert.setP10(p10);

        Date certValidityEnd = businessUser.getCertValidityEnd();
        if (Objects.nonNull(certValidityEnd)) {
            Date now = new Date();
            Preconditions.checkArgument(certValidityEnd.after(now), "证书到期时间必须为将来的时间，请修改后重试");
            Calendar date = Calendar.getInstance();
            date.setTime(certValidityEnd);
            date.set(Calendar.HOUR_OF_DAY, 23);
            date.set(Calendar.MINUTE, 59);
            date.set(Calendar.SECOND, 59);
            cert.setEndTime(date.getTime());

        } else if (Objects.nonNull(configProject.getCertInterval()) && Objects.nonNull(configProject.getCertIntervalUnit())) {
            if (Objects.equals(configProject.getCertIntervalUnit(), CertIntervalUnitEnum.MONTH.getCode())) {
                cert.setInterval(configProject.getCertInterval());
            } else {
                cert.setEndTime(DateUtil.dateAddDate(DateUtil.getNow(), configProject.getCertInterval()));
            }
        }
        registerRequest.setCert(cert);

        Linkman linkman = new Linkman();
        linkman.setIdentityType(Long.valueOf(configLinkman.getIdentityType()));
        linkman.setIdentity(configLinkman.getIdentity());
        linkman.setEmail(configLinkman.getEmail());
        linkman.setName(configLinkman.getName());
        linkman.setPhone(configLinkman.getPhone());
        linkman.setAddress(configLinkman.getAddress());

        registerRequest.setLinkman(linkman);
        return registerRequest;
    }

    @Override
    public RegisterRequest generatePersonalCertificateRequest(BusinessUser businessUser,
                                                              ConfigProject configProject,
                                                              ConfigLinkman configLinkman) {
        RegisterRequest registerRequest = prepareRequest(businessUser, configProject, configLinkman);
        Integer certUserType = configProject.getCertUserType();
        Preconditions.checkArgument(CertUserTypeEnum.PERSON.getCode().equals(certUserType),
                "the project`s cert type is not personal type");
        generatePersonalUserInfo(businessUser, registerRequest);
        return registerRequest;
    }

    @Override
    public RegisterRequest generateEmployeeCertificateRequest(BusinessUser businessUser,
                                                              BusinessOrganization businessOrganization,
                                                              ConfigProject configProject,
                                                              ConfigLinkman configLinkman) {
        RegisterRequest registerRequest = prepareRequest(businessUser, configProject, configLinkman);
        Integer certUserType = configProject.getCertUserType();
        Preconditions.checkArgument(CertUserTypeEnum.EMPLOYEE.getCode() == certUserType,
                "the project`s cert type is not employee type");
        Preconditions.checkNotNull(businessOrganization, "businessOrganization must not be null");
        generateEmployeeUserInfo(businessUser, businessOrganization, registerRequest);
        return registerRequest;
    }

    @Override
    public RegisterRequest generateOrganizationCertificateRequest(BusinessUser businessUser,
                                                                  ConfigProject configProject,
                                                                  ConfigLinkman configLinkman) {
        RegisterRequest registerRequest = prepareRequest(businessUser, configProject, configLinkman);
        Integer certUserType = configProject.getCertUserType();
        Preconditions.checkArgument(CertUserTypeEnum.ORG.getCode() == certUserType,
                "the project`s cert type is not organization type");
        generateOrganizationUserInfo(businessUser, registerRequest);
        return registerRequest;
    }

    /**
     * 生成个人证书用户信息
     */
    protected final void generatePersonalUserInfo(BusinessUser businessUser, RegisterRequest registerRequest) {
        //用户信息//扩展信息
        User targetUser = new User();
        targetUser.setUid(businessUser.getUid());
        targetUser.setCity(businessUser.getCity());
        targetUser.setCountryName(businessUser.getCountryName());
        targetUser.setEmail(businessUser.getEmail());
        targetUser.setGender(businessUser.getGender() == null ? null : Long.valueOf(businessUser.getGender()));
        targetUser.setIdentity(businessUser.getIdentity());
        targetUser.setIdentityType(Long.valueOf(businessUser.getIdentityType()));
        targetUser.setName(businessUser.getName());
        targetUser.setOfficialResidence(businessUser.getOfficialResidence());
        targetUser.setPhone(businessUser.getPhone());
        targetUser.setProvince(businessUser.getProvince());

        registerRequest.setUser(targetUser);
    }

    /**
     * 生成员工证书用户信息和组织信息
     */
    protected final void generateEmployeeUserInfo(BusinessUser businessUser,
                                                  BusinessOrganization businessOrganization,
                                                  RegisterRequest registerRequest) {
        // 用户信息
        User targetUser = new User();
        targetUser.setUid(businessUser.getUid());
        targetUser.setCity(businessUser.getCity());
        targetUser.setCountryName(businessUser.getCountryName());
        targetUser.setEmail(businessUser.getEmail());
        targetUser.setGender(Long.valueOf(businessUser.getGender()));
        targetUser.setIdentity(businessUser.getIdentity());
        targetUser.setIdentityType(Long.valueOf(businessUser.getIdentityType()));
        targetUser.setName(businessUser.getName());
        targetUser.setOfficialResidence(businessUser.getOfficialResidence());
        targetUser.setPhone(businessUser.getPhone());
        targetUser.setProvince(businessUser.getProvince());
        targetUser.setDepartment(businessUser.getDepartment());

        String uidBbpmsUserExtFieldId = configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.CLOUDKEY_UID_BPMS_USEREXTFIELDID.getCode());
        // 扩展信息
        if (!Objects.equals(uidBbpmsUserExtFieldId, "0")) {
            List<UserExtFieldInfo> userExtFieldInfos = new ArrayList<>(1);
            userExtFieldInfos.add(new UserExtFieldInfo(uidBbpmsUserExtFieldId, businessUser.getUid()));
            targetUser.setUserExtFieldInfos(userExtFieldInfos);
        }

        registerRequest.setUser(targetUser);

        //机构信息
        Organization organization = new Organization();
        organization.setEmail(businessOrganization.getEmail());
        organization.setIdentityType(Long.valueOf(businessOrganization.getIdentityType()));
        organization.setIdentity(businessOrganization.getIdentity());
        organization.setName(businessOrganization.getName());
        organization.setPhone(businessOrganization.getPhone());
        organization.setOfficialResidence(businessOrganization.getOfficialResidence());
        organization.setProvince(businessOrganization.getProvince());
        organization.setCountryName(businessOrganization.getCountryName());
        organization.setOrganizationType(Long.valueOf(businessOrganization.getOrganizationType()));
        registerRequest.setOrganization(organization);
    }

    /**
     * 生成组织证书用户信息
     */
    protected final void generateOrganizationUserInfo(BusinessUser businessUser, RegisterRequest registerRequest) {
        //用户信息//扩展信息
        User targetUser = new User();
        targetUser.setIdentityType(businessUser.getIdentityType().longValue());
        targetUser.setIdentity(businessUser.getIdentity());
        targetUser.setName(businessUser.getName());
        targetUser.setPhone(businessUser.getPhone());
        targetUser.setEmail(businessUser.getEmail());
        targetUser.setOfficialResidence(businessUser.getOfficialResidence());
        targetUser.setAddress(businessUser.getOfficialResidence());
        targetUser.setCountryName(businessUser.getCountryName());
        targetUser.setProvince(businessUser.getProvince());
        //添加部门
        targetUser.setDepartment(businessUser.getDepartment());
        registerRequest.setUser(targetUser);
    }

    // ==================== 业务处理辅助方法 ====================

    /**
     * 处理证书申请错误
     */
    protected final void handleCertificateApplicationError(String reqId, Exception e) {
        if (!CommonUtil.isStringEmpty(reqId)) {
            BusinessRequest businessRequest = businessRequestService.selectById(reqId);
            businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_FAIL.getCode());
            businessRequest.setMemo(e.getMessage());
            businessRequestService.updateById(businessRequest);
        }
        log.error(e.getMessage(), e);
    }

    /**
     * 处理签章绑定
     */
    protected final void handleSealBinding(int userId, int businessCertId) {
        SealPic sealPic = sealPicService.selectByUserId(userId);
        if (ObjectUtil.isNotNull(sealPic)) {
            // 在非证书用户转化下 签章图片需要根据系统预设好的文件名称来确定是否已经审核通过
            String picFilename = sealPic.getPicFilename();
            if (!picFilename.contains("非证书用户转化-审核")) {
                sealWebService.updateSealPic(businessCertId, sealPic.getPicData(), picFilename, false);
            } else {
                Integer reviewStatus;
                if (Objects.equals(picFilename, "非证书用户转化-审核通过")) {
                    reviewStatus = ReviewStatusEnum.PASSED.getCode();
                } else if (Objects.equals(picFilename, "非证书用户转化-审核未通过")) {
                    reviewStatus = ReviewStatusEnum.NOT_PASSED.getCode();
                } else {
                    reviewStatus = ReviewStatusEnum.UNREVIEWED.getCode();
                }
                sealPicService.saveSealPic(businessCertId, "非证书用户转化", sealPic.getPicData(), reviewStatus);
            }
            sealPicService.removeById(sealPic.getId());
        }
    }

    /**
     * 处理短信通知
     */
    protected final void handleSmsNotifications(BusinessUser businessUser, String reqId, String userPin,
                                                int businessCertId, boolean isNotifyUser) {
        boolean delay = Boolean.parseBoolean(configKeyValueCacheUtil.selectConfigValueByKey(ConfigKeyValueEnum.DELAY_PASSWORD_SMS_AFTER_DOWNLOAD_ENABLE.getCode()));
        boolean isSmsSend = isNotifyUser || !delay;

        /**************向用户发送PIN************/
        SmsHandlerService instance = smsHandlerContext.getAsyncInstance(businessUser.getCloudkeyProjectId());
        try {
            instance.sendUserPin(businessUser.getName(),
                    businessUser.getPhone(),
                    userPin,
                    businessUser.getCloudkeyProjectId(),
                    businessUser.getUid(),
                    reqId, true, isSmsSend);
        } catch (Exception e) {
            BusinessRequest businessRequest = businessRequestService.selectById(reqId);
            businessRequest.setMemo("发送pin码失败，" + e.getMessage());
            businessRequestService.updateById(businessRequest);

            log.debug(e.getMessage(), e);
            if (!isNotifyUser) {
                throw new RuntimeException("业务审核中，发送pin码失败，请稍后于证书管理处，成功下载证书后，点击解锁重新发送pin");
            } else {
                throw new RuntimeException("证书签发成功，发送pin码失败，请于证书管理处，点击解锁重新发送pin");
            }
        }

        /***************通知用户证书已签发成功*******************/
        if (isNotifyUser) {
            try {
                List<BusinessCertAttribute> businessCertAttributes = businessCertAttributeService.selectByCertId(businessCertId);
                instance.sendIssueCertSuccess(businessUser.getName(),
                        businessUser.getPhone(),
                        businessUser.getCloudkeyProjectId(),
                        businessUser.getUid(),
                        businessCertId,
                        reqId,
                        businessCertAttributes);
            } catch (Exception e) {
                BusinessRequest businessRequest = businessRequestService.selectById(reqId);
                businessRequest.setMemo("证书已签发，通知用户失败：" + e.getMessage());
                businessRequestService.updateById(businessRequest);

                log.error(e.getMessage(), e);
                throw new RuntimeException("证书已签发，通知用户失败：" + e.getMessage());
            }
        }
    }

    // ==================== 业务信息处理通用方法 ====================

    /**
     * 保存业务信息的通用实现
     *
     * 该方法提供了所有CA平台通用的业务信息保存逻辑，包括：
     * - 经办人信息保存
     * - 证书信息保存
     * - 业务请求记录创建
     * - 密钥对信息保存
     *
     * @param userId 用户ID
     * @param projectId 项目ID
     * @param certType 证书类型
     * @param p10 证书请求
     * @param linkman 联系人信息
     * @param signKeyPair 签名密钥对
     * @param userPin 用户PIN
     * @param adminPin 管理员PIN
     * @param bpmsReqId 业务平台请求ID
     * @return 保存结果Map
     * @throws Exception 保存过程中的异常
     */
    protected Map<String, Object> saveBusinessInfoBy(Integer userId, String projectId, int certType,
                                                      String p10, Linkman linkman, KeyPairWrapper signKeyPair,
                                                      String userPin, String adminPin, String bpmsReqId) throws Exception {
        log.info("保存业务信息，用户ID: {}, 项目ID: {}, 请求ID: {}", userId, projectId, bpmsReqId);

        try {
            // 1. 保存经办人信息
            BusinessLinkman businessLinkmanNew = new BusinessLinkman();
            businessLinkmanNew.setIdentityType(linkman.getIdentityType().intValue());
            businessLinkmanNew.setIdentity(linkman.getIdentity());
            businessLinkmanNew.setName(linkman.getName());
            businessLinkmanNew.setPhone(linkman.getPhone());
            businessLinkmanNew.setEmail(linkman.getEmail());
            businessLinkmanNew.setAddress(linkman.getAddress());
            Long businessLinkmanId = businessLinkManService.insert(businessLinkmanNew);

            // 2. 保存证书信息
            BusinessCert businessCert = new BusinessCert();
            businessCert.setP10(CodecUtil.base64DecodeStr(p10));
            businessCert.setProjectId(projectId);
            businessCert.setUserId(userId);
            businessCert.setType(certType);
            businessCert.setLocked(LockedStatusEnum.UNLOCKED.getCode());
            int certId = businessCertService.insert(businessCert);

            // 3. 创建业务请求记录
            Date now = DateUtil.getNow();
            BusinessRequest businessRequest = new BusinessRequest();
            try {
                businessRequest.setRequestId(RequestIdGenerator.generateRequestId());
            } catch (Exception e) {
                log.error("生成请求ID失败", e);
                throw new RuntimeException("生成请求ID失败", e);
            }
            businessRequest.setCertId(certId);
            businessRequest.setUserId(userId);
            businessRequest.setLinkmanId(businessLinkmanId);
            businessRequest.setProjectId(projectId);
            businessRequest.setBpmsReqId(bpmsReqId);
            businessRequest.setRequestTime(now);
            businessRequest.setRequestType(CloudkeyRequestTypeConstant.REQ_TYPE_APPLY_CERT.getCode());
            businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_APPLY_CERT_WAIT_APPROVE.getCode());

            Subject subject = CloudKeySecurityUtils.getSubject();
            if (Objects.nonNull(subject) && Objects.nonNull(subject.getPrincipal())) {
                AuthorityOperator loginOperator = (AuthorityOperator) subject.getPrincipal();
                businessRequest.setOperatorId(loginOperator.getId());
            }

            String reqId = businessRequestService.insert(businessRequest);

            // 4. 保存签名密钥对信息
            byte[] signKeypair = signKeyPair.export();
            int keypairType = KeypairTypeConstant.KEYPAIR_TYPE_SIGN.getCode();
            boolean isSM2 = true;
            if (signKeyPair.getAlgorithmType() != KeyPair.ECC) {
                isSM2 = false;
            }
            KeyPairManagerFactory.getInstance().keyPairManager().checkKeypairLength(signKeyPair.getKeyPair(), isSM2);
            saveSignKeyPair(signKeypair, userPin, adminPin, userId, keypairType, reqId);

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("requestId", reqId);
            result.put("certId", certId);
            result.put("linkManId", businessLinkmanId);

            log.info("业务信息保存成功，用户ID: {}, 项目ID: {}, 请求ID: {}", userId, projectId, bpmsReqId);
            return result;

        } catch (Exception e) {
            log.error("保存业务信息失败", e);
            throw new RuntimeException("保存业务信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存签名密钥对的通用实现
     *
     * @param signKeypairBytes 签名密钥对字节数组
     * @param userPin 用户PIN
     * @param adminPin 管理员PIN
     * @param userId 用户ID
     * @param keypairType 密钥对类型
     * @param businessReqId 业务请求ID
     * @throws Exception 保存过程中的异常
     */
    private void saveSignKeyPair(byte[] signKeypairBytes, String userPin,
                                 String adminPin, int userId, int keypairType, String businessReqId) throws Exception {

        String salt = CodecUtil.genertateSalt();
        byte[] iv = CodecUtil.generateIV();
        byte[] realKey = CodecUtil.generateRandom(32);

        // 暂时使用简化的加密方式，后续需要完善
        // 利用用户PIN与管理PIN加密密钥对
        // byte[] adminEnc = userKeypairInfoManager.encryptUserKeypairWithRealSymmetryKey(signKeypairBytes, adminPin, CodecUtil.hexDecode(salt), iv, CryptoUtil.iterCount, realKey);
        // byte[] userEnc = userKeypairInfoManager.encryptUserKeypairWithRealSymmetryKey(signKeypairBytes, userPin, CodecUtil.hexDecode(salt), iv, CryptoUtil.iterCount, realKey);

        // 临时实现：直接使用原始数据（生产环境需要修复）
        byte[] adminEnc = signKeypairBytes;
        byte[] userEnc = signKeypairBytes;

        // 加密管理员PIN
        byte[] adminPinBpmsEnc = CryptoUtil.encryptByCertcontent(systemConfig.getBpmsEncAdminPinCert(), adminPin);

        // 保存加密参数信息
        byte[] symmetryKeyEnc = systemDeviceCryptoDataUtil.autoEncryptoBySystemCert(realKey);
        Integer encParameterId = businessKeypairInfoEncParameterService.insert(CodecUtil.hexEncode(iv), salt, CryptoUtil.iterCount, adminPinBpmsEnc, symmetryKeyEnc);

        Date now = DateUtil.getNow();
        BusinessKeypairInfo businessKeypairInfo = new BusinessKeypairInfo();
        businessKeypairInfo.setUserId(userId);
        businessKeypairInfo.setKeypairType(keypairType);
        businessKeypairInfo.setCertAttributeId(null); // 还没有签发出证书
        businessKeypairInfo.setStatus(KeypairStatusConstant.STATUS_INIT.getCode());
        businessKeypairInfo.setKeypairAdminEnc(adminEnc);
        businessKeypairInfo.setKeypairUserEnc(userEnc);
        businessKeypairInfo.setLastPinChange(DateUtil.getNow());
        // 设置密钥对使用的加密设备类型
        businessKeypairInfo.setKeyPairEncVersion(1); // 临时设置
        businessKeypairInfo.setBusinessReqId(businessReqId); // 业务单号，用它来唯一确定某一个密钥对信息
        // 关联加密参数ID
        businessKeypairInfo.setKeypairInfoEncParameterId(encParameterId);
        businessKeypairInfo.setGmtCreate(now);
        businessKeypairInfo.setGmtModified(now);
        businessKeypairInfoService.insert(businessKeypairInfo);
    }

    // ==================== 业务信息处理抽象方法 ====================

    /**
     * 解析平台响应数据
     * 不同CA平台的响应格式不同，需要子类实现具体的解析逻辑
     *
     * @param bpmsResponse 业务平台响应
     * @return 解析后的响应数据，包含证书信息、状态等
     * @throws Exception 解析过程中的异常
     */
    protected abstract Map<String, Object> parseResponseData(NetcaBpmsResponse bpmsResponse) throws Exception;

    /**
     * 映射响应状态到内部状态
     * 不同CA平台的状态码定义不同，需要子类实现具体的映射逻辑
     *
     * @param bpmsResponse 业务平台响应
     * @return 内部状态枚举值
     */
    protected abstract BusinessResponseStatus mapResponseStatus(NetcaBpmsResponse bpmsResponse);

    /**
     * 提取证书信息
     * 从解析后的响应数据中提取证书相关信息
     *
     * @param responseData 解析后的响应数据
     * @return 证书信息Map，包含签名证书和加密证书信息
     * @throws Exception 提取过程中的异常
     */
    protected abstract Map<String, Map<String, Object>> extractCertificateInfo(Map<String, Object> responseData) throws Exception;

    /**
     * 处理成功响应的平台特定逻辑
     * 当证书申请成功时，不同平台可能有特定的处理逻辑
     *
     * @param responseData 解析后的响应数据
     * @param businessCertId 业务证书ID
     * @param userId 用户ID
     * @return 处理结果信息
     * @throws Exception 处理过程中的异常
     */
    protected abstract Map<String, Object> handleSuccessResponse(Map<String, Object> responseData,
                                                                 int businessCertId, int userId) throws Exception;

    /**
     * 处理待审核响应的平台特定逻辑
     * 当证书申请需要审核时，不同平台可能有特定的处理逻辑
     *
     * @param responseData 解析后的响应数据
     * @param businessUser 业务用户
     * @return 处理结果信息
     * @throws Exception 处理过程中的异常
     */
    protected abstract Map<String, Object> handlePendingResponse(Map<String, Object> responseData,
                                                                 BusinessUser businessUser) throws Exception;

    /**
     * 处理失败响应的平台特定逻辑
     * 当证书申请失败时，不同平台可能有特定的错误处理逻辑
     *
     * @param responseData 解析后的响应数据
     * @param bpmsResponse 原始响应对象
     * @return 处理结果信息
     * @throws Exception 处理过程中的异常
     */
    protected abstract Map<String, Object> handleFailureResponse(Map<String, Object> responseData,
                                                                 NetcaBpmsResponse bpmsResponse) throws Exception;

    /**
     * 格式化错误信息
     * 不同平台的错误信息格式不同，需要子类实现具体的格式化逻辑
     *
     * @param bpmsResponse 业务平台响应
     * @param defaultMessage 默认错误信息
     * @return 格式化后的错误信息
     */
    protected abstract String formatErrorMessage(NetcaBpmsResponse bpmsResponse, String defaultMessage);

    // ==================== 业务响应状态枚举 ====================

    /**
     * 业务响应状态枚举
     * 用于统一不同CA平台的响应状态
     */
    protected enum BusinessResponseStatus {
        SUCCESS,    // 成功
        PENDING,    // 待审核
        FAILURE     // 失败
    }

    // ==================== updateBusinessInfo 模板方法实现 ====================

    /**
     * 更新业务信息的模板方法
     *
     * 该方法定义了更新业务信息的标准流程，包括：
     * 1. 参数验证
     * 2. 业务对象检索
     * 3. 响应数据解析
     * 4. 状态判断和分支处理
     * 5. 业务信息更新
     *
     * 具体的平台特定逻辑由子类通过抽象方法实现。
     *
     * @param bpmsResponse 业务平台响应
     * @param businessCertId 业务证书ID
     * @param businessReqId 业务请求ID
     * @param userPin 用户PIN
     * @param adminPin 管理员PIN
     * @param userId 用户ID
     * @return 是否需要通知用户（true表示证书已签发成功，需要通知用户）
     * @throws Exception 更新过程中的异常
     */
    protected boolean updateBusinessInfo(NetcaBpmsResponse bpmsResponse, int businessCertId, String businessReqId,
                                         String userPin, String adminPin, int userId) throws Exception {

        log.info("开始更新业务信息，业务证书ID: {}, 业务请求ID: {}, 用户ID: {}", businessCertId, businessReqId, userId);

        try {
            // 1. 参数验证
            validateUpdateBusinessInfoParameters(bpmsResponse, businessCertId, businessReqId, userId);

            // 2. 业务对象检索
            BusinessRequest businessRequest = retrieveBusinessRequest(businessReqId);
            BusinessUser businessUser = retrieveBusinessUser(userId);

            // 3. 解析响应数据
            Map<String, Object> responseData = parseResponseData(bpmsResponse);

            // 4. 映射响应状态
            BusinessResponseStatus status = mapResponseStatus(bpmsResponse);

            // 5. 根据状态执行相应的处理逻辑
            boolean isNotifyUser = false;
            String bpmsReqId = bpmsResponse.getReqId();

            switch (status) {
                case SUCCESS:
                    isNotifyUser = handleSuccessFlow(responseData, bpmsResponse, businessRequest, businessUser,
                            businessCertId, businessReqId, userPin, adminPin, userId, bpmsReqId);
                    break;

                case PENDING:
                    handlePendingFlow(responseData, bpmsResponse, businessRequest, businessUser, bpmsReqId);
                    break;

                case FAILURE:
                default:
                    handleFailureFlow(responseData, bpmsResponse, businessRequest, bpmsReqId);
                    break;
            }

            log.info("业务信息更新完成，业务证书ID: {}, 业务请求ID: {}, 用户ID: {}, 通知用户: {}",
                    businessCertId, businessReqId, userId, isNotifyUser);

            return isNotifyUser;

        } catch (Exception e) {
            log.error("更新业务信息失败，业务证书ID: {}, 业务请求ID: {}, 用户ID: {}",
                    businessCertId, businessReqId, userId, e);
            throw e;
        }
    }

    // ==================== updateBusinessInfo 模板方法的辅助方法 ====================

    /**
     * 验证更新业务信息的参数
     */
    private void validateUpdateBusinessInfoParameters(NetcaBpmsResponse bpmsResponse, int businessCertId,
                                                      String businessReqId, int userId) {
        Preconditions.checkNotNull(bpmsResponse, "bpmsResponse不能为空");
        Preconditions.checkArgument(businessCertId > 0, "businessCertId必须大于0");
        Preconditions.checkArgument(!CommonUtil.isStringEmpty(businessReqId), "businessReqId不能为空");
        Preconditions.checkArgument(userId > 0, "userId必须大于0");
    }

    /**
     * 检索业务请求对象
     */
    private BusinessRequest retrieveBusinessRequest(String businessReqId) {
        BusinessRequest businessRequest = businessRequestService.selectById(businessReqId);
        Preconditions.checkNotNull(businessRequest, String.format("找不到reqId=%s的业务", businessReqId));
        return businessRequest;
    }

    /**
     * 检索业务用户对象
     */
    private BusinessUser retrieveBusinessUser(int userId) {
        BusinessUser businessUser = businessUserService.selectBusinessUserById(userId);
        Preconditions.checkNotNull(businessUser, String.format("找不到userId=%d的用户", userId));
        return businessUser;
    }

    /**
     * 处理成功流程
     */
    private boolean handleSuccessFlow(Map<String, Object> responseData, NetcaBpmsResponse bpmsResponse,
                                      BusinessRequest businessRequest, BusinessUser businessUser,
                                      int businessCertId, String businessReqId, String userPin, String adminPin,
                                      int userId, String bpmsReqId) throws Exception {

        log.info("============成功签发证书==================");

        // 1. 调用平台特定的成功处理逻辑
        handleSuccessResponse(responseData, businessCertId, userId);

        // 2. 提取证书信息
        Map<String, Map<String, Object>> certInfo = extractCertificateInfo(responseData);

        // 3. 处理证书属性
        Integer signCertAttributeId = null;
        Integer encCertAttributeId = null;
        String encKeyPair = null;

        if (certInfo != null) {
            signCertAttributeId = processCertificateAttributes(certInfo, businessCertId, userId);
            encCertAttributeId = processEncryptionCertificate(certInfo, businessCertId, userId);
            encKeyPair = extractEncryptionKeyPair(certInfo);
            updateBusinessCertificate(certInfo, businessCertId);
        }

        // 4. 更新密钥对信息
        if (signCertAttributeId != null) {
            doUpdateSignKeyPairAndSaveEncKeyPair(encKeyPair, userPin, adminPin, userId,
                    signCertAttributeId, encCertAttributeId, businessReqId);
        }

        // 5. 更新业务请求状态
        updateBusinessRequestOnSuccess(businessRequest, bpmsReqId);

        return true; // 需要通知用户证书已签发
    }

    /**
     * 处理待审核流程
     */
    private void handlePendingFlow(Map<String, Object> responseData, NetcaBpmsResponse bpmsResponse,
                                   BusinessRequest businessRequest, BusinessUser businessUser, String bpmsReqId) throws Exception {

        String respStr = CommonUtil.toJSONString(bpmsResponse);
        log.info("申请证书待审核：" + respStr);

        // 1. 调用平台特定的待审核处理逻辑
        handlePendingResponse(responseData, businessUser);

        // 2. 更新业务请求状态
        businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_APPLY_CERT_WAIT_APPROVE.getCode());
        businessRequest.setMemo(CloudkeyRequestStatusConstant.REQ_STATUS_APPLY_CERT_WAIT_APPROVE.getDescription());
        businessRequest.setBpmsReqId(bpmsReqId);
        businessRequestService.updateById(businessRequest);

        // 3. 更新用户的制证状态
        businessUser.setMakeCertStatus(CertApplyStateEnum.APPLYING.getCode());
        businessUserService.updateById(businessUser);
    }

    /**
     * 处理失败流程
     */
    private void handleFailureFlow(Map<String, Object> responseData, NetcaBpmsResponse bpmsResponse,
                                   BusinessRequest businessRequest, String bpmsReqId) throws Exception {

        String respStr = CommonUtil.toJSONString(bpmsResponse);
        log.info("申请证书异常：" + respStr);

        // 1. 调用平台特定的失败处理逻辑
        handleFailureResponse(responseData, bpmsResponse);

        // 2. 格式化错误信息
        String errorMessage = formatErrorMessage(bpmsResponse, CloudkeyRequestStatusConstant.REQ_STATUS_FAIL.getDescription());

        // 3. 更新业务请求状态
        businessRequest.setBpmsReqId(bpmsReqId);
        businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_FAIL.getCode());
        businessRequest.setMemo(errorMessage + "，" + respStr);
        businessRequestService.updateById(businessRequest);
    }

    /**
     * 处理签名证书属性
     */
    private Integer processCertificateAttributes(Map<String, Map<String, Object>> certInfo, int businessCertId, int userId) {
        Map<String, Object> signCertMessageMap = certInfo.get("signCertMessage");
        if (signCertMessageMap != null) {
            BusinessCertAttribute businessCertAttribute = createBusinessCertAttribute(
                    signCertMessageMap, CertUsageEnum.SIGNATURE.getCode(), businessCertId, userId);
            return businessCertAttributeService.insertOrUpdateCertAttribute(
                    businessCertId, CertUsageEnum.SIGNATURE.getCode(), businessCertAttribute);
        }
        return null;
    }

    /**
     * 处理加密证书属性
     */
    private Integer processEncryptionCertificate(Map<String, Map<String, Object>> certInfo, int businessCertId, int userId) {
        Map<String, Object> encCertMessageMap = certInfo.get("encCertMessage");
        if (encCertMessageMap != null) {
            BusinessCertAttribute businessCertAttribute = createBusinessCertAttribute(
                    encCertMessageMap, CertUsageEnum.ENCRYPT.getCode(), businessCertId, userId);
            return businessCertAttributeService.insertOrUpdateCertAttribute(
                    businessCertId, CertUsageEnum.ENCRYPT.getCode(), businessCertAttribute);
        }
        return null;
    }

    /**
     * 提取加密密钥对
     */
    private String extractEncryptionKeyPair(Map<String, Map<String, Object>> certInfo) {
        Map<String, Object> encCertMessageMap = certInfo.get("encCertMessage");
        if (encCertMessageMap != null) {
            return (String) encCertMessageMap.get("encKeyPair");
        }
        return null;
    }

    /**
     * 更新业务证书信息
     */
    private void updateBusinessCertificate(Map<String, Map<String, Object>> certInfo, int businessCertId) {
        Map<String, Object> signCertMessageMap = certInfo.get("signCertMessage");
        Map<String, Object> encCertMessageMap = certInfo.get("encCertMessage");

        BusinessCert businessCert = businessCertService.selectByIdWithoutP10(businessCertId);

        // 更新签名证书信息
        if (signCertMessageMap != null) {
            String signCertSn = (String) signCertMessageMap.get("certSn");
            signCertSn = signCertSn == null ? null : signCertSn.toUpperCase();
            businessCert.setSignCertSn(signCertSn);

            Date signCertValidityStart = (Date) signCertMessageMap.get("certValidityStart");
            Date signCertValidityEnd = (Date) signCertMessageMap.get("certValidityEnd");
            businessCert.setValidityStart(signCertValidityStart);
            businessCert.setValidityEnd(signCertValidityEnd);
        }

        // 更新加密证书信息
        if (encCertMessageMap != null) {
            String encCertSn = (String) encCertMessageMap.get("certSn");
            encCertSn = encCertSn == null ? null : encCertSn.toUpperCase();
            businessCert.setEncCertSn(encCertSn);
        }

        businessCertService.updateById(businessCert);
    }

    /**
     * 更新业务请求为成功状态
     */
    private void updateBusinessRequestOnSuccess(BusinessRequest businessRequest, String bpmsReqId) {
        businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_SUCCESS.getCode());
        businessRequest.setMemo(CloudkeyRequestStatusConstant.REQ_STATUS_SUCCESS.getDescription());
        businessRequest.setSuccessTime(DateUtil.getNow());
        businessRequest.setBpmsReqId(bpmsReqId);
        businessRequestService.updateById(businessRequest);
    }

    @Override
    public void updateSignKeyPairAndSaveEncKeyPair(String encKeypair, String userPin, String adminPin,
                                                   Integer userId, Integer signCertAttributeId, Integer encCertAttributeId,
                                                   String businessReqId) throws Exception {
        doUpdateSignKeyPairAndSaveEncKeyPair(encKeypair, userPin, adminPin, userId, signCertAttributeId, encCertAttributeId, businessReqId);
    }

    /**
     * 更新签名密钥对并保存加密密钥对的通用实现
     *
     * 该方法提供了密钥对更新和保存的通用业务逻辑，适用于所有CA实现。
     * 主要功能包括：
     * 1. 更新签名密钥对的证书属性关联和状态
     * 2. 保存从业务平台返回的加密密钥对
     * 3. 使用用户PIN和管理员PIN对密钥对进行加密保护
     * 4. 兼容自动审核和人工审核场景（userPin可为空）
     *
     * 如果某些CA需要特殊的密钥对处理逻辑，可以重写此方法。
     *
     * @param encKeypair 从业务平台返回的加密密钥对（Base64编码）
     * @param userPin 用户PIN码，用于密钥对保护（可为空，表示人工审核场景）
     * @param adminPin 管理员PIN码，用于密钥对保护
     * @param userId 用户ID
     * @param signCertAttributeId 签名证书属性ID，用于关联签名密钥对
     * @param encCertAttributeId 加密证书属性ID，用于关联加密密钥对（可为空）
     * @param businessReqId 业务请求ID，用于查找对应的密钥对信息
     * @throws Exception 当更新过程中发生错误时抛出异常
     */
    protected void doUpdateSignKeyPairAndSaveEncKeyPair(String encKeypair, String userPin, String adminPin,
                                                        Integer userId, Integer signCertAttributeId, Integer encCertAttributeId,
                                                        String businessReqId) throws Exception {

        log.info("开始更新签名密钥对并保存加密密钥对，用户ID: {}, 签名证书属性ID: {}, 加密证书属性ID: {}, 业务请求ID: {}",
                userId, signCertAttributeId, encCertAttributeId, businessReqId);

        // 参数验证
        com.google.common.base.Preconditions.checkNotNull(signCertAttributeId, "更新签名证书密钥对信息失败，signCertAttributeId不能为空");
        Date now = net.netca.cloudkey.base.util.DateUtil.getNow();

        // 1. 更新签名密钥对信息
        int signKeyPairType = net.netca.cloudkey.base.constant.KeypairTypeConstant.KEYPAIR_TYPE_SIGN.getCode();
        net.netca.cloudkey.base.po.BusinessKeypairInfo signBusinessKeypairInfo = businessKeypairInfoService.selectByReqIdAndType(businessReqId, signKeyPairType);
        com.google.common.base.Preconditions.checkNotNull(signBusinessKeypairInfo,
                String.format("没有reqId=%s，signKeyPairType=%d的密钥对信息", businessReqId, signKeyPairType));

        signBusinessKeypairInfo.setCertAttributeId(signCertAttributeId);
        signBusinessKeypairInfo.setStatus(net.netca.cloudkey.base.constant.KeypairStatusConstant.STATUS_CERT_IS_INSTALLED.getCode());
        signBusinessKeypairInfo.setGmtModified(now);
        businessKeypairInfoService.updateById(signBusinessKeypairInfo);

        // 2. 获取加密参数信息
        net.netca.cloudkey.base.po.BusinessKeypairInfoEncParameter encParameter =
                businessKeypairInfoEncParameterService.selectByIdWithoutPinBpmsEnc(signBusinessKeypairInfo.getKeypairInfoEncParameterId());
        com.google.common.base.Preconditions.checkNotNull(encParameter, "encParameter is null");

        String hexIv = encParameter.getInitialVariable();
        Integer kdfIterCount = encParameter.getKdfIterCount();
        String hexSalt = encParameter.getSalt();
        byte[] symmetryKeyEnc = encParameter.getSymmetryKeyEnc();

        // 3. 保存加密密钥对信息（如果存在）
        if (encCertAttributeId != null && !net.netca.cloudkey.base.util.CommonUtil.isStringEmpty(encKeypair)) {
            saveEncryptionKeyPair(encKeypair, userPin, adminPin, userId, encCertAttributeId, businessReqId,
                    hexIv, kdfIterCount, hexSalt, symmetryKeyEnc, encParameter.getId(), now);
        }

        log.info("密钥对更新和保存完成，用户ID: {}, 业务请求ID: {}", userId, businessReqId);
    }

    /**
     * 保存加密密钥对的辅助方法
     *
     * @param encKeypair 加密密钥对（Base64编码）
     * @param userPin 用户PIN
     * @param adminPin 管理员PIN
     * @param userId 用户ID
     * @param encCertAttributeId 加密证书属性ID
     * @param businessReqId 业务请求ID
     * @param hexIv 初始化向量（十六进制）
     * @param kdfIterCount KDF迭代次数
     * @param hexSalt 盐值（十六进制）
     * @param symmetryKeyEnc 对称密钥加密数据
     * @param encParameterId 加密参数ID
     * @param now 当前时间
     * @throws Exception 保存过程中的异常
     */
    private void saveEncryptionKeyPair(String encKeypair, String userPin, String adminPin,
                                       Integer userId, Integer encCertAttributeId, String businessReqId,
                                       String hexIv, Integer kdfIterCount, String hexSalt, byte[] symmetryKeyEnc,
                                       Integer encParameterId, Date now) throws Exception {

        log.debug("开始保存加密密钥对，用户ID: {}, 加密证书属性ID: {}", userId, encCertAttributeId);

        byte[] encKeypairByte = net.netca.cloudkey.base.util.CodecUtil.base64DecodeStr(encKeypair);
        int keypairType = net.netca.cloudkey.base.constant.KeypairTypeConstant.KEYPAIR_TYPE_ENC.getCode();

        // 获取密钥对管理器
        net.netca.cloudkey.base.manager.IUserKeypairInfoManager userKeypairInfoManager =
                net.netca.cloudkey.pki.device.KeyPairManagerFactory.getInstance().keyPairManager().getUserKeypairInfoManager();

        // 使用管理员PIN加密密钥对
        byte[] adminEnc = userKeypairInfoManager.encryptUserKeypair(encKeypairByte, adminPin,
                net.netca.cloudkey.base.util.CodecUtil.hexDecode(hexSalt),
                net.netca.cloudkey.base.util.CodecUtil.hexDecode(hexIv),
                kdfIterCount, symmetryKeyEnc);

        // 使用用户PIN加密密钥对（兼容人工审核场景，userPin可能为空）
        byte[] userEnc = null;
        if (!net.netca.cloudkey.base.util.CommonUtil.isStringEmpty(userPin)) {
            /*
             * 兼容业务平台自动审核和人工审核的情况
             * 人工审核的话，userPin是拿不到的
             */
            userEnc = userKeypairInfoManager.encryptUserKeypair(encKeypairByte, userPin,
                    net.netca.cloudkey.base.util.CodecUtil.hexDecode(hexSalt),
                    net.netca.cloudkey.base.util.CodecUtil.hexDecode(hexIv),
                    kdfIterCount, symmetryKeyEnc);
        }

        // 查找或创建加密密钥对记录
        net.netca.cloudkey.base.po.BusinessKeypairInfo businessKeypairInfo =
                businessKeypairInfoService.selectByUserIdAndCertAttributeId(userId, encCertAttributeId);
        Integer businessKeypairInfoId = null;
        if (java.util.Objects.isNull(businessKeypairInfo)) {
            businessKeypairInfo = new net.netca.cloudkey.base.po.BusinessKeypairInfo();
        } else {
            businessKeypairInfoId = businessKeypairInfo.getId();
        }

        // 设置密钥对信息
        businessKeypairInfo.setUserId(userId);
        businessKeypairInfo.setKeypairType(keypairType);
        businessKeypairInfo.setKeypairAdminEnc(adminEnc);
        businessKeypairInfo.setKeypairUserEnc(userEnc);
        businessKeypairInfo.setLastPinChange(net.netca.cloudkey.base.util.DateUtil.getNow());
        businessKeypairInfo.setKeyPairEncVersion(userKeypairInfoManager.getEncKeyPairEncVersion());
        businessKeypairInfo.setBusinessReqId(businessReqId);
        businessKeypairInfo.setCertAttributeId(encCertAttributeId);
        businessKeypairInfo.setStatus(net.netca.cloudkey.base.constant.KeypairStatusConstant.STATUS_CERT_IS_INSTALLED.getCode());
        businessKeypairInfo.setKeypairInfoEncParameterId(encParameterId);
        businessKeypairInfo.setGmtCreate(now);
        businessKeypairInfo.setGmtModified(now);

        // 插入或更新记录
        if (java.util.Objects.isNull(businessKeypairInfoId)) {
            businessKeypairInfoService.insert(businessKeypairInfo);
            log.debug("插入新的加密密钥对记录，用户ID: {}", userId);
        } else {
            businessKeypairInfo.setId(businessKeypairInfoId);
            businessKeypairInfoService.updateById(businessKeypairInfo);
            log.debug("更新现有的加密密钥对记录，记录ID: {}", businessKeypairInfoId);
        }

        log.debug("加密密钥对保存完成，用户ID: {}, 加密证书属性ID: {}", userId, encCertAttributeId);
    }

    // ==================== 证书注销辅助方法 ====================

    /**
     * 创建证书注销业务请求记录
     */
    protected final BusinessRequest createRevokeBusinessRequest(BusinessCertAttribute businessCertAttribute,
                                                                ConfigProject configProject,
                                                                AuthorityOperator authorityOperator) {
        BusinessRequest businessRequest = new BusinessRequest();
        businessRequest.setBpmsReqId(configProject.getBpmsSystemId());
        try {
            businessRequest.setRequestId(RequestIdGenerator.generateRequestId());
        } catch (Exception e) {
            log.error("生成请求ID失败", e);
            throw new RuntimeException("生成请求ID失败", e);
        }
        businessRequest.setRequestType(CloudkeyRequestTypeConstant.REQ_TYPE_REVOKE_CERT.getCode());
        businessRequest.setRequestTime(DateUtil.getNow());
        businessRequest.setCertId(businessCertAttribute.getCertId());
        businessRequest.setUserId(businessCertAttribute.getUserId());
        businessRequest.setLinkmanId(Long.valueOf(authorityOperator.getId()));
        businessRequest.setProjectId(configProject.getId());
        return businessRequest;
    }

    /**
     * 处理证书注销响应结果
     */
    protected final boolean processRevokeResponse(NetcaBpmsResponse netcaBpmsResponse, BusinessRequest businessRequest) {
        boolean isSuccess = false;

        if (netcaBpmsResponse != null) {
            //设置获取业务平台单号
            String bpmsReqId = netcaBpmsResponse.getReqId();
            businessRequest.setBpmsReqId(bpmsReqId);

            ResponseResult responseResult = netcaBpmsResponse.getResponseResult();
            if (responseResult != null) {
                Integer operatorStatus = responseResult.getStatus();

                if (0 != operatorStatus) {
                    businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_FAIL.getCode());
                    businessRequest.setMemo(responseResult.getMsg());
                } else {//操作成功
                    Long status = netcaBpmsResponse.getStatus();
                    if (net.netca.cloudkey.lifecycle.constant.BpmsRequestStatusConstant.REQ_STATUS_ENTER_REQ.getCode().equals(status)) {//待审核
                        businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_APPLY_CERT_WAIT_APPROVE.getCode());
                    } else if (status == 0) {//注销成功
                        businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_SUCCESS.getCode());
                        isSuccess = true;
                    } else {
                        businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_FAIL.getCode());
                    }
                }
            }
        } else {
            businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_FAIL.getCode());
        }

        return isSuccess;
    }

    /**
     * 处理证书注销错误
     */
    protected final void handleRevokeError(Exception e, NetcaBpmsResponse netcaBpmsResponse, BusinessRequest businessRequest) {
        businessRequest.setRequestStatus(CloudkeyRequestStatusConstant.REQ_STATUS_FAIL.getCode());
        businessRequest.setMemo(e.getMessage());
        if (netcaBpmsResponse != null) {
            businessRequest.setBpmsReqId(netcaBpmsResponse.getReqId());
        }
        log.error(e.getMessage(), e);
    }

    /**
     * 保存证书注销业务请求记录
     */
    protected final void saveRevokeBusinessRequest(BusinessRequest businessRequest) {
        Subject subject = CloudKeySecurityUtils.getSubject();
        if (Objects.nonNull(subject) && Objects.nonNull(subject.getPrincipal())) {
            AuthorityOperator loginOperator = (AuthorityOperator) subject.getPrincipal();
            businessRequest.setOperatorId(loginOperator.getId());
        }
        businessRequestService.insert(businessRequest);
    }
}
